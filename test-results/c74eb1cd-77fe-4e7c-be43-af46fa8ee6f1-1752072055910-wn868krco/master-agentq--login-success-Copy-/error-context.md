# Page snapshot

```yaml
- complementary:
  - link "AgentQ":
    - /url: /
    - heading "AgentQ" [level=1]
  - button "‹"
  - navigation:
    - link "📁 Projects":
      - /url: "#"
  - link "🚪 Sign Out":
    - /url: "#"
- main:
  - text: Welcome, <EMAIL>
  - heading "Projects" [level=2]
  - paragraph: Manage your test case generation projects.
  - heading "Create Project" [level=3]
  - text: Project Name
  - textbox "Project Name"
  - text: Description
  - textbox "Description"
  - button "Create Project" [disabled]
  - textbox "Search projects..."
  - button "Search"
  - table:
    - rowgroup:
      - row "Name Description Created By Created At Actions":
        - cell "Name"
        - cell "Description"
        - cell "Created By"
        - cell "Created At"
        - cell "Actions"
    - rowgroup:
      - row "<EMAIL> <EMAIL> <EMAIL> 7/9/2025 ✏️":
        - cell "<EMAIL>"
        - cell "<EMAIL>"
        - cell "<EMAIL>"
        - cell "7/9/2025"
        - cell "✏️":
          - button "✏️"
      - row "WebSocket Test Project Project for websocket test runner noviantonugroho 7/9/2025 ✏️":
        - cell "WebSocket Test Project"
        - cell "Project for websocket test runner"
        - cell "noviantonugroho"
        - cell "7/9/2025"
        - cell "✏️":
          - button "✏️"
      - row "WebSocket Test Project Project for websocket test runner noviantonugroho 7/9/2025 ✏️":
        - cell "WebSocket Test Project"
        - cell "Project for websocket test runner"
        - cell "noviantonugroho"
        - cell "7/9/2025"
        - cell "✏️":
          - button "✏️"
      - row "test noviantonugroho 6/7/2025 ✏️":
        - cell "test"
        - cell
        - cell "noviantonugroho"
        - cell "6/7/2025"
        - cell "✏️":
          - button "✏️"
```